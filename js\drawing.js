// Drawing Tool for StockScreener Application

let canvas, ctx;
let isDrawing = false;
let currentTool = 'pen';
let currentColor = '#000000';
let currentSize = 3;
let startX, startY;
let drawings = [];
let currentDrawing = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeDrawingTool();
});

function initializeDrawingTool() {
    canvas = document.getElementById('drawing-canvas');
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    
    // Set canvas size
    resizeCanvas();
    
    // Setup event listeners
    setupCanvasEvents();
    setupToolbarEvents();
    
    // Initialize canvas
    clearCanvas();
}

function resizeCanvas() {
    const container = canvas.parentElement;
    const rect = container.getBoundingClientRect();
    
    // Set canvas size to fit container
    canvas.width = Math.min(800, rect.width - 40);
    canvas.height = 600;
    
    // Set canvas style
    canvas.style.maxWidth = '100%';
    canvas.style.height = 'auto';
}

function setupCanvasEvents() {
    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);
    
    // Prevent scrolling when touching the canvas
    canvas.addEventListener('touchstart', function(e) {
        e.preventDefault();
    });
    canvas.addEventListener('touchend', function(e) {
        e.preventDefault();
    });
    canvas.addEventListener('touchmove', function(e) {
        e.preventDefault();
    });
}

function setupToolbarEvents() {
    // Tool buttons
    const toolButtons = document.querySelectorAll('.tool-btn');
    toolButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            toolButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Set current tool
            currentTool = this.getAttribute('data-tool');
            updateCursor();
        });
    });
    
    // Color picker
    const colorPicker = document.getElementById('color-picker');
    if (colorPicker) {
        colorPicker.addEventListener('change', function() {
            currentColor = this.value;
        });
    }
    
    // Brush size
    const brushSize = document.getElementById('brush-size');
    if (brushSize) {
        brushSize.addEventListener('input', function() {
            currentSize = parseInt(this.value);
        });
    }
    
    // Clear button
    const clearButton = document.getElementById('clear-canvas');
    if (clearButton) {
        clearButton.addEventListener('click', function() {
            if (confirm('Apakah Anda yakin ingin menghapus semua gambar?')) {
                clearCanvas();
                drawings = [];
            }
        });
    }
    
    // Save button
    const saveButton = document.getElementById('save-drawing');
    if (saveButton) {
        saveButton.addEventListener('click', saveDrawing);
    }
}

function getMousePos(e) {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    return {
        x: (e.clientX - rect.left) * scaleX,
        y: (e.clientY - rect.top) * scaleY
    };
}

function getTouchPos(e) {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    return {
        x: (e.touches[0].clientX - rect.left) * scaleX,
        y: (e.touches[0].clientY - rect.top) * scaleY
    };
}

function startDrawing(e) {
    isDrawing = true;
    const pos = getMousePos(e);
    startX = pos.x;
    startY = pos.y;
    
    if (currentTool === 'pen' || currentTool === 'eraser') {
        ctx.beginPath();
        ctx.moveTo(startX, startY);
    }
    
    // Start new drawing object
    currentDrawing = {
        tool: currentTool,
        color: currentColor,
        size: currentSize,
        points: [{ x: startX, y: startY }]
    };
}

function draw(e) {
    if (!isDrawing) return;
    
    const pos = getMousePos(e);
    
    switch (currentTool) {
        case 'pen':
            drawPen(pos.x, pos.y);
            break;
        case 'eraser':
            erase(pos.x, pos.y);
            break;
        case 'line':
            redrawCanvas();
            drawLine(startX, startY, pos.x, pos.y);
            break;
        case 'rectangle':
            redrawCanvas();
            drawRectangle(startX, startY, pos.x, pos.y);
            break;
        case 'circle':
            redrawCanvas();
            drawCircle(startX, startY, pos.x, pos.y);
            break;
    }
    
    // Add point to current drawing
    if (currentDrawing) {
        currentDrawing.points.push({ x: pos.x, y: pos.y });
    }
}

function stopDrawing(e) {
    if (!isDrawing) return;
    
    isDrawing = false;
    
    // Save the drawing
    if (currentDrawing && currentDrawing.points.length > 1) {
        drawings.push({ ...currentDrawing });
    }
    
    currentDrawing = null;
    
    // Handle text tool
    if (currentTool === 'text') {
        const pos = getMousePos(e);
        addText(pos.x, pos.y);
    }
}

function handleTouch(e) {
    e.preventDefault();
    
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                     e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    
    canvas.dispatchEvent(mouseEvent);
}

function drawPen(x, y) {
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = currentColor;
    ctx.lineWidth = currentSize;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    ctx.lineTo(x, y);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(x, y);
}

function erase(x, y) {
    ctx.globalCompositeOperation = 'destination-out';
    ctx.lineWidth = currentSize * 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    ctx.lineTo(x, y);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(x, y);
}

function drawLine(x1, y1, x2, y2) {
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = currentColor;
    ctx.lineWidth = currentSize;
    ctx.lineCap = 'round';
    
    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
}

function drawRectangle(x1, y1, x2, y2) {
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = currentColor;
    ctx.lineWidth = currentSize;
    
    const width = x2 - x1;
    const height = y2 - y1;
    
    ctx.beginPath();
    ctx.rect(x1, y1, width, height);
    ctx.stroke();
}

function drawCircle(x1, y1, x2, y2) {
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = currentColor;
    ctx.lineWidth = currentSize;
    
    const radius = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    
    ctx.beginPath();
    ctx.arc(x1, y1, radius, 0, 2 * Math.PI);
    ctx.stroke();
}

function addText(x, y) {
    const text = prompt('Masukkan teks:');
    if (text) {
        ctx.globalCompositeOperation = 'source-over';
        ctx.fillStyle = currentColor;
        ctx.font = `${currentSize * 6}px Arial`;
        ctx.fillText(text, x, y);
        
        // Save text as drawing
        drawings.push({
            tool: 'text',
            color: currentColor,
            size: currentSize,
            text: text,
            x: x,
            y: y
        });
    }
}

function redrawCanvas() {
    clearCanvas();
    
    // Redraw all saved drawings
    drawings.forEach(drawing => {
        redrawDrawing(drawing);
    });
}

function redrawDrawing(drawing) {
    if (!drawing.points && !drawing.text) return;
    
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = drawing.color;
    ctx.lineWidth = drawing.size;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    if (drawing.tool === 'text') {
        ctx.fillStyle = drawing.color;
        ctx.font = `${drawing.size * 6}px Arial`;
        ctx.fillText(drawing.text, drawing.x, drawing.y);
        return;
    }
    
    if (drawing.points.length < 2) return;
    
    switch (drawing.tool) {
        case 'pen':
            ctx.beginPath();
            ctx.moveTo(drawing.points[0].x, drawing.points[0].y);
            for (let i = 1; i < drawing.points.length; i++) {
                ctx.lineTo(drawing.points[i].x, drawing.points[i].y);
            }
            ctx.stroke();
            break;
            
        case 'line':
            const firstPoint = drawing.points[0];
            const lastPoint = drawing.points[drawing.points.length - 1];
            ctx.beginPath();
            ctx.moveTo(firstPoint.x, firstPoint.y);
            ctx.lineTo(lastPoint.x, lastPoint.y);
            ctx.stroke();
            break;
            
        case 'rectangle':
            const start = drawing.points[0];
            const end = drawing.points[drawing.points.length - 1];
            const width = end.x - start.x;
            const height = end.y - start.y;
            ctx.beginPath();
            ctx.rect(start.x, start.y, width, height);
            ctx.stroke();
            break;
            
        case 'circle':
            const center = drawing.points[0];
            const edge = drawing.points[drawing.points.length - 1];
            const radius = Math.sqrt(Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2));
            ctx.beginPath();
            ctx.arc(center.x, center.y, radius, 0, 2 * Math.PI);
            ctx.stroke();
            break;
    }
}

function clearCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set white background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
}

function updateCursor() {
    switch (currentTool) {
        case 'pen':
            canvas.style.cursor = 'crosshair';
            break;
        case 'eraser':
            canvas.style.cursor = 'grab';
            break;
        case 'text':
            canvas.style.cursor = 'text';
            break;
        default:
            canvas.style.cursor = 'crosshair';
    }
}

function saveDrawing() {
    // Create download link
    const link = document.createElement('a');
    link.download = `stock-drawing-${new Date().getTime()}.png`;
    link.href = canvas.toDataURL();
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    alert('Gambar berhasil disimpan!');
}

// Handle window resize
window.addEventListener('resize', function() {
    setTimeout(resizeCanvas, 100);
});
