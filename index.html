<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockScreener - Platform Screening Saham Indonesia</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <span>StockScreener</span>
            </div>
            <nav class="nav">
                <ul class="nav-list">
                    <li><a href="#dashboard" class="nav-link active" data-section="dashboard">Dashboard</a></li>
                    <li><a href="#ipo" class="nav-link" data-section="ipo">IPO Terbaru</a></li>
                    <li><a href="#potential" class="nav-link" data-section="potential">Potensi Naik</a></li>
                    <li><a href="#gainers" class="nav-link" data-section="gainers">Top Gainers</a></li>
                    <li><a href="#calculator" class="nav-link" data-section="calculator">Kalkulator P&L</a></li>
                    <li><a href="#drawing" class="nav-link" data-section="drawing">Drawing Tool</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="section active">
            <div class="container">
                <h1 class="section-title">Dashboard Screening Saham</h1>
                <div class="dashboard-grid">
                    <div class="card market-summary">
                        <div class="card-header">
                            <h3>Ringkasan Pasar</h3>
                            <div class="real-time-indicator">
                                <span class="status-dot" id="market-status"></span>
                                <span class="status-text" id="market-status-text">Loading...</span>
                                <button class="refresh-btn" id="refresh-market" title="Refresh Data">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="market-stats" id="market-stats">
                            <div class="stat">
                                <span class="stat-label">IHSG</span>
                                <span class="stat-value" id="ihsg-value">7,245.67</span>
                                <span class="stat-change positive" id="ihsg-change">+1.23%</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">Volume</span>
                                <span class="stat-value" id="market-volume">12.5B</span>
                                <span class="stat-change">shares</span>
                            </div>
                        </div>
                        <div class="last-update" id="market-last-update">
                            Last updated: <span id="market-timestamp">--</span>
                        </div>
                    </div>

                    <div class="card quick-access">
                        <h3>Akses Cepat</h3>
                        <div class="quick-buttons">
                            <button class="btn btn-primary" data-section="ipo">
                                <i class="fas fa-rocket"></i>
                                IPO Terbaru
                            </button>
                            <button class="btn btn-success" data-section="potential">
                                <i class="fas fa-trending-up"></i>
                                Potensi Naik
                            </button>
                            <button class="btn btn-warning" data-section="gainers">
                                <i class="fas fa-fire"></i>
                                Top Gainers
                            </button>
                        </div>
                    </div>
                </div>

                <div class="featured-stocks">
                    <h3>Saham Pilihan</h3>
                    <div class="stock-grid" id="featured-stocks-grid">
                        <!-- Stock cards will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- IPO Section -->
        <section id="ipo" class="section">
            <div class="container">
                <h1 class="section-title">IPO Saham Terbaru</h1>
                <div class="ipo-list" id="ipo-list">
                    <!-- IPO items will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Potential Stocks Section -->
        <section id="potential" class="section">
            <div class="container">
                <h1 class="section-title">Saham Berpotensi Naik</h1>
                <div class="stock-list" id="potential-stocks">
                    <!-- Potential stocks will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Top Gainers Section -->
        <section id="gainers" class="section">
            <div class="container">
                <h1 class="section-title">Top Gainers Hari Ini</h1>
                <div class="gainers-list" id="gainers-list">
                    <!-- Top gainers will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Calculator Section -->
        <section id="calculator" class="section">
            <div class="container">
                <h1 class="section-title">Kalkulator Keuntungan & Kerugian</h1>
                <div class="calculator-container">
                    <div class="card calculator-card">
                        <form id="profit-loss-form">
                            <div class="form-group">
                                <label for="stock-code">Kode Saham</label>
                                <input type="text" id="stock-code" placeholder="Contoh: BBCA">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="buy-price">Harga Beli (Rp)</label>
                                    <input type="number" id="buy-price" placeholder="0">
                                </div>
                                <div class="form-group">
                                    <label for="sell-price">Harga Jual (Rp)</label>
                                    <input type="number" id="sell-price" placeholder="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="lots">Jumlah Lot</label>
                                    <input type="number" id="lots" placeholder="1">
                                </div>
                                <div class="form-group">
                                    <label for="broker-fee">Biaya Broker (%)</label>
                                    <input type="number" id="broker-fee" placeholder="0.15" step="0.01">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Hitung P&L</button>
                        </form>

                        <div id="calculation-result" class="calculation-result">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Drawing Tool Section -->
        <section id="drawing" class="section">
            <div class="container">
                <h1 class="section-title">Drawing & Writing Tool</h1>
                <div class="drawing-container">
                    <div class="drawing-toolbar">
                        <button class="tool-btn active" data-tool="pen">
                            <i class="fas fa-pen"></i> Pen
                        </button>
                        <button class="tool-btn" data-tool="eraser">
                            <i class="fas fa-eraser"></i> Eraser
                        </button>
                        <button class="tool-btn" data-tool="text">
                            <i class="fas fa-font"></i> Text
                        </button>
                        <button class="tool-btn" data-tool="line">
                            <i class="fas fa-minus"></i> Line
                        </button>
                        <button class="tool-btn" data-tool="rectangle">
                            <i class="fas fa-square"></i> Rectangle
                        </button>
                        <button class="tool-btn" data-tool="circle">
                            <i class="fas fa-circle"></i> Circle
                        </button>
                        <input type="color" id="color-picker" value="#000000">
                        <input type="range" id="brush-size" min="1" max="20" value="3">
                        <button class="btn btn-secondary" id="clear-canvas">Clear</button>
                        <button class="btn btn-primary" id="save-drawing">Save</button>
                    </div>
                    <canvas id="drawing-canvas" width="800" height="600"></canvas>
                </div>
            </div>
        </section>

        <!-- Stock Profile Modal -->
        <div id="stock-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <div id="stock-profile">
                    <!-- Stock profile content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/data.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/chart.js"></script>
    <script src="js/calculator.js"></script>
    <script src="js/drawing.js"></script>
    <script src="js/main.js"></script>
</body>

</html>