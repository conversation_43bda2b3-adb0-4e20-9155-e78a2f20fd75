// API Service Layer for StockScreener

class APIService {
    constructor() {
        this.cache = new Map();
        this.lastCallTime = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
    }

    // Main method to get stock data
    async getStockData(symbol, dataType = 'quote') {
        try {
            const cacheKey = `${symbol}_${dataType}`;
            
            // Check cache first
            if (this.isCacheValid(cacheKey, dataType)) {
                console.log(`Cache hit for ${cacheKey}`);
                return this.cache.get(cacheKey).data;
            }

            // Add to queue to respect rate limits
            return await this.queueRequest(() => this.fetchStockData(symbol, dataType));
            
        } catch (error) {
            console.error(`Error getting stock data for ${symbol}:`, error);
            return this.getFallbackData(symbol, dataType);
        }
    }

    // Fetch data from Yahoo Finance API
    async fetchStockData(symbol, dataType) {
        const yahooSymbol = STOCK_SYMBOLS[symbol] || `${symbol}.JK`;
        
        try {
            let data;
            
            switch (dataType) {
                case 'quote':
                    data = await this.fetchYahooQuote(yahooSymbol);
                    break;
                case 'chart':
                    data = await this.fetchYahooChart(yahooSymbol);
                    break;
                case 'fundamental':
                    data = await this.fetchFundamentalData(symbol);
                    break;
                default:
                    throw new Error(`Unknown data type: ${dataType}`);
            }

            // Cache the result
            this.cacheData(`${symbol}_${dataType}`, data, dataType);
            return data;
            
        } catch (error) {
            console.error(`API fetch failed for ${symbol}:`, error);
            throw error;
        }
    }

    // Fetch quote data from Yahoo Finance
    async fetchYahooQuote(yahooSymbol) {
        const url = `${CORS_SOLUTIONS.active}${API_CONFIG.yahoo.quotesUrl}?symbols=${yahooSymbol}`;
        
        const response = await this.makeRequest(url, {
            timeout: API_CONFIG.yahoo.timeout
        });

        if (!response.quoteResponse || !response.quoteResponse.result || response.quoteResponse.result.length === 0) {
            throw new Error('No quote data received');
        }

        const quote = response.quoteResponse.result[0];
        
        return {
            symbol: quote.symbol.replace('.JK', ''),
            price: quote.regularMarketPrice || quote.ask || 0,
            change: quote.regularMarketChange || 0,
            changePercent: quote.regularMarketChangePercent || 0,
            volume: quote.regularMarketVolume || 0,
            marketCap: quote.marketCap || 0,
            previousClose: quote.regularMarketPreviousClose || 0,
            dayHigh: quote.regularMarketDayHigh || 0,
            dayLow: quote.regularMarketDayLow || 0,
            timestamp: Date.now()
        };
    }

    // Fetch chart data from Yahoo Finance
    async fetchYahooChart(yahooSymbol, period = '1d', interval = '1m') {
        const url = `${CORS_SOLUTIONS.active}${API_CONFIG.yahoo.baseUrl}${yahooSymbol}?period1=0&period2=9999999999&interval=${interval}`;
        
        const response = await this.makeRequest(url, {
            timeout: API_CONFIG.yahoo.timeout
        });

        if (!response.chart || !response.chart.result || response.chart.result.length === 0) {
            throw new Error('No chart data received');
        }

        const chart = response.chart.result[0];
        const timestamps = chart.timestamp || [];
        const prices = chart.indicators.quote[0];
        
        return {
            symbol: yahooSymbol.replace('.JK', ''),
            timestamps: timestamps,
            prices: {
                open: prices.open || [],
                high: prices.high || [],
                low: prices.low || [],
                close: prices.close || [],
                volume: prices.volume || []
            },
            timestamp: Date.now()
        };
    }

    // Fetch fundamental data (fallback to Alpha Vantage if available)
    async fetchFundamentalData(symbol) {
        // For now, return enhanced static data with real-time price
        const quoteData = await this.fetchYahooQuote(STOCK_SYMBOLS[symbol]);
        
        // Get static fundamental data and merge with real-time price
        const staticData = this.getStaticFundamental(symbol);
        
        return {
            ...staticData,
            price: quoteData.price,
            change: quoteData.change,
            changePercent: quoteData.changePercent,
            volume: quoteData.volume,
            marketCap: quoteData.marketCap || staticData.marketCap,
            timestamp: Date.now()
        };
    }

    // Get multiple stocks data efficiently
    async getMultipleStocks(symbols, dataType = 'quote') {
        const promises = symbols.map(symbol => 
            this.getStockData(symbol, dataType).catch(error => {
                console.error(`Failed to fetch ${symbol}:`, error);
                return this.getFallbackData(symbol, dataType);
            })
        );
        
        return await Promise.all(promises);
    }

    // Queue system for rate limiting
    async queueRequest(requestFunction) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ requestFunction, resolve, reject });
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const { requestFunction, resolve, reject } = this.requestQueue.shift();
            
            try {
                // Respect rate limits
                await this.waitForRateLimit();
                
                const result = await requestFunction();
                resolve(result);
                
            } catch (error) {
                reject(error);
            }
        }

        this.isProcessingQueue = false;
    }

    // Rate limiting
    async waitForRateLimit() {
        const now = Date.now();
        const lastCall = this.lastCallTime.get('yahoo') || 0;
        const timeSinceLastCall = now - lastCall;
        const minInterval = API_CONFIG.yahoo.rateLimit;

        if (timeSinceLastCall < minInterval) {
            const waitTime = minInterval - timeSinceLastCall;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        this.lastCallTime.set('yahoo', Date.now());
    }

    // HTTP request with timeout and retries
    async makeRequest(url, options = {}) {
        const { timeout = 10000, retries = 3 } = options;
        
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);
                
                const response = await fetch(url, {
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': 'StockScreener/1.0'
                    }
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
                
            } catch (error) {
                console.error(`Request attempt ${attempt} failed:`, error);
                
                if (attempt === retries) {
                    throw error;
                }
                
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    // Cache management
    cacheData(key, data, dataType) {
        const ttl = CACHE_CONFIG[`${dataType}Data`] || CACHE_CONFIG.priceData;
        
        this.cache.set(key, {
            data: data,
            timestamp: Date.now(),
            ttl: ttl
        });

        // Clean old cache entries
        this.cleanCache();
    }

    isCacheValid(key, dataType) {
        if (!this.cache.has(key)) {
            return false;
        }

        const cached = this.cache.get(key);
        const age = Date.now() - cached.timestamp;
        
        return age < cached.ttl;
    }

    cleanCache() {
        if (this.cache.size <= CACHE_CONFIG.maxSize) {
            return;
        }

        // Remove oldest entries
        const entries = Array.from(this.cache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        const toRemove = entries.slice(0, entries.length - CACHE_CONFIG.maxSize);
        toRemove.forEach(([key]) => this.cache.delete(key));
    }

    // Fallback to static data
    getFallbackData(symbol, dataType) {
        console.log(`Using fallback data for ${symbol}`);
        
        // Find static data for the symbol
        const allStocks = [
            ...stockData.featured,
            ...stockData.ipo,
            ...stockData.potential,
            ...stockData.gainers
        ];
        
        const staticStock = allStocks.find(stock => stock.code === symbol);
        
        if (staticStock) {
            return {
                ...staticStock,
                timestamp: Date.now(),
                isStatic: true
            };
        }
        
        // Return default data if not found
        return {
            code: symbol,
            name: `${symbol} Stock`,
            price: 0,
            change: 0,
            changePercent: 0,
            volume: 0,
            timestamp: Date.now(),
            isStatic: true,
            error: 'Data not available'
        };
    }

    // Get static fundamental data
    getStaticFundamental(symbol) {
        const allStocks = [
            ...stockData.featured,
            ...stockData.ipo,
            ...stockData.potential,
            ...stockData.gainers
        ];
        
        return allStocks.find(stock => stock.code === symbol) || {};
    }

    // Check if market is open
    isMarketOpen() {
        const now = new Date();
        const jakartaTime = new Date(now.toLocaleString("en-US", {timeZone: MARKET_HOURS.timezone}));
        
        const day = jakartaTime.getDay();
        const hour = jakartaTime.getHours();
        const minute = jakartaTime.getMinutes();
        
        // Check if it's a trading day
        if (!MARKET_HOURS.tradingDays.includes(day)) {
            return false;
        }
        
        // Check if it's within trading hours
        const currentTime = hour * 60 + minute;
        const openTime = MARKET_HOURS.open.hour * 60 + MARKET_HOURS.open.minute;
        const closeTime = MARKET_HOURS.close.hour * 60 + MARKET_HOURS.close.minute;
        
        return currentTime >= openTime && currentTime <= closeTime;
    }

    // Clear all cache
    clearCache() {
        this.cache.clear();
        console.log('Cache cleared');
    }
}

// Create global instance
const apiService = new APIService();
