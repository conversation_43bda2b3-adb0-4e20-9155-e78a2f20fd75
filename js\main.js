// Main JavaScript for StockScreener Application

document.addEventListener('DOMContentLoaded', function () {
    initializeApp();
});

function initializeApp() {
    setupNavigation();
    setupMobileMenu();
    setupRealTimeFeatures();
    loadDashboard();
    loadIPOStocks();
    loadPotentialStocks();
    loadTopGainers();
    setupStockModal();
    setupRefreshButtons();
}

function setupRealTimeFeatures() {
    // Subscribe to real-time data updates
    dataManager.subscribe('featured', updateFeaturedStocks, UPDATE_INTERVALS.dashboard);
    dataManager.subscribe('ipo', updateIPOStocks, UPDATE_INTERVALS.dashboard);
    dataManager.subscribe('potential', updatePotentialStocks, UPDATE_INTERVALS.dashboard);
    dataManager.subscribe('gainers', updateTopGainers, UPDATE_INTERVALS.topGainers);
    dataManager.subscribe('market', updateMarketData, UPDATE_INTERVALS.marketOverview);

    // Initial data load
    loadRealTimeData();

    // Setup market status indicator
    updateMarketStatus();
    setInterval(updateMarketStatus, 60000); // Update every minute
}

function setupRefreshButtons() {
    // Market refresh button
    const marketRefreshBtn = document.getElementById('refresh-market');
    if (marketRefreshBtn) {
        marketRefreshBtn.addEventListener('click', () => refreshData('market'));
    }

    // Add refresh buttons to other sections
    addRefreshButton('dashboard', 'featured-stocks-grid');
    addRefreshButton('ipo', 'ipo-list');
    addRefreshButton('potential', 'potential-stocks');
    addRefreshButton('gainers', 'gainers-list');
}

function addRefreshButton(sectionId, containerId) {
    const section = document.getElementById(sectionId);
    if (!section) return;

    const title = section.querySelector('.section-title');
    if (!title) return;

    const refreshBtn = document.createElement('button');
    refreshBtn.className = 'refresh-btn';
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
    refreshBtn.title = 'Refresh Data';
    refreshBtn.onclick = () => refreshData(getSectionDataType(sectionId));

    title.appendChild(refreshBtn);
}

function getSectionDataType(sectionId) {
    const mapping = {
        'dashboard': 'featured',
        'ipo': 'ipo',
        'potential': 'potential',
        'gainers': 'gainers'
    };
    return mapping[sectionId] || sectionId;
}

async function loadRealTimeData() {
    try {
        showLoadingState();

        // Load all data types
        await Promise.all([
            dataManager.updateData('featured'),
            dataManager.updateData('ipo'),
            dataManager.updateData('potential'),
            dataManager.updateData('gainers'),
            dataManager.updateData('market')
        ]);

        hideLoadingState();

    } catch (error) {
        console.error('Failed to load real-time data:', error);
        hideLoadingState();
        showErrorMessage('Failed to load real-time data. Using cached data.');
    }
}

async function refreshData(dataType) {
    const refreshBtn = event?.target?.closest('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.classList.add('spinning');
    }

    try {
        await dataManager.refreshData(dataType);
        showSuccessMessage(`${dataType} data refreshed`);
    } catch (error) {
        console.error(`Failed to refresh ${dataType}:`, error);
        showErrorMessage(`Failed to refresh ${dataType} data`);
    } finally {
        if (refreshBtn) {
            refreshBtn.classList.remove('spinning');
        }
    }
}

// Navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const quickButtons = document.querySelectorAll('[data-section]');

    navLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
            updateActiveNav(this);
        });
    });

    quickButtons.forEach(button => {
        button.addEventListener('click', function () {
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
            updateActiveNav(document.querySelector(`[data-section="${sectionId}"].nav-link`));
        });
    });
}

function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
}

function updateActiveNav(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// Mobile menu functionality
function setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navList = document.querySelector('.nav-list');

    mobileToggle.addEventListener('click', function () {
        navList.style.display = navList.style.display === 'flex' ? 'none' : 'flex';
    });
}

// Dashboard functionality
function loadDashboard() {
    const featuredGrid = document.getElementById('featured-stocks-grid');
    if (!featuredGrid) return;

    featuredGrid.innerHTML = '';

    stockData.featured.forEach(stock => {
        const stockCard = createStockCard(stock);
        featuredGrid.appendChild(stockCard);
    });
}

// Real-time update functions
function updateFeaturedStocks(stocks) {
    const featuredGrid = document.getElementById('featured-stocks-grid');
    if (!featuredGrid) return;

    featuredGrid.innerHTML = '';

    stocks.forEach(stock => {
        const stockCard = createStockCard(stock);
        featuredGrid.appendChild(stockCard);
    });

    updateLastUpdateTime('featured');
}

function updateIPOStocks(stocks) {
    const ipoList = document.getElementById('ipo-list');
    if (!ipoList) return;

    ipoList.innerHTML = '';

    stocks.forEach(stock => {
        const ipoCard = createIPOCard(stock);
        ipoList.appendChild(ipoCard);
    });

    updateLastUpdateTime('ipo');
}

function updatePotentialStocks(stocks) {
    const potentialList = document.getElementById('potential-stocks');
    if (!potentialList) return;

    potentialList.innerHTML = '';

    stocks.forEach(stock => {
        const potentialCard = createPotentialCard(stock);
        potentialList.appendChild(potentialCard);
    });

    updateLastUpdateTime('potential');
}

function updateTopGainers(stocks) {
    const gainersList = document.getElementById('gainers-list');
    if (!gainersList) return;

    gainersList.innerHTML = '';

    stocks.forEach(stock => {
        const gainerCard = createStockCard(stock);
        gainersList.appendChild(gainerCard);
    });

    updateLastUpdateTime('gainers');
}

function updateMarketData(marketData) {
    // Update IHSG value
    const ihsgValue = document.getElementById('ihsg-value');
    const ihsgChange = document.getElementById('ihsg-change');
    const marketVolume = document.getElementById('market-volume');

    if (ihsgValue) {
        ihsgValue.textContent = formatNumber(marketData.ihsg.value);
    }

    if (ihsgChange) {
        const changePercent = marketData.ihsg.changePercent;
        const changeClass = changePercent >= 0 ? 'positive' : 'negative';
        const changeSign = changePercent >= 0 ? '+' : '';

        ihsgChange.textContent = `${changeSign}${changePercent.toFixed(2)}%`;
        ihsgChange.className = `stat-change ${changeClass}`;
    }

    if (marketVolume) {
        marketVolume.textContent = formatVolume(marketData.ihsg.volume);
    }

    updateLastUpdateTime('market');
    updateMarketStatus(marketData.isRealTime);
}

function updateMarketStatus(isRealTime = null) {
    const statusDot = document.getElementById('market-status');
    const statusText = document.getElementById('market-status-text');

    if (!statusDot || !statusText) return;

    const isMarketOpen = apiService.isMarketOpen();

    if (isRealTime === false) {
        statusDot.className = 'status-dot offline';
        statusText.textContent = 'Offline Data';
    } else if (isMarketOpen) {
        statusDot.className = 'status-dot online';
        statusText.textContent = 'Market Open';
    } else {
        statusDot.className = 'status-dot closed';
        statusText.textContent = 'Market Closed';
    }
}

function updateLastUpdateTime(dataType) {
    const timestamp = document.getElementById(`${dataType}-timestamp`) ||
        document.getElementById('market-timestamp');

    if (timestamp) {
        const now = new Date();
        timestamp.textContent = now.toLocaleTimeString('id-ID');
    }
}

// IPO stocks functionality
function loadIPOStocks() {
    const ipoList = document.getElementById('ipo-list');
    if (!ipoList) return;

    ipoList.innerHTML = '';

    stockData.ipo.forEach(stock => {
        const ipoCard = createIPOCard(stock);
        ipoList.appendChild(ipoCard);
    });
}

// Potential stocks functionality
function loadPotentialStocks() {
    const potentialList = document.getElementById('potential-stocks');
    if (!potentialList) return;

    potentialList.innerHTML = '';

    stockData.potential.forEach(stock => {
        const potentialCard = createPotentialCard(stock);
        potentialList.appendChild(potentialCard);
    });
}

// Top gainers functionality
function loadTopGainers() {
    const gainersList = document.getElementById('gainers-list');
    if (!gainersList) return;

    gainersList.innerHTML = '';

    stockData.gainers.forEach(stock => {
        const gainerCard = createStockCard(stock);
        gainersList.appendChild(gainerCard);
    });
}

// Create stock card element
function createStockCard(stock) {
    const card = document.createElement('div');
    card.className = 'stock-card';
    card.onclick = () => openStockProfile(stock);

    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';

    card.innerHTML = `
        <div class="stock-header">
            <div>
                <div class="stock-code">${stock.code}</div>
                <div class="stock-name">${stock.name}</div>
            </div>
            <div class="stock-price">Rp ${formatNumber(stock.price)}</div>
        </div>
        <div class="stock-change ${changeClass}">
            ${changeSign}${stock.change} (${changeSign}${stock.changePercent.toFixed(2)}%)
        </div>
        <div class="stock-info">
            <span>Volume: ${formatVolume(stock.volume)}</span>
            <span>P/E: ${stock.pe}</span>
        </div>
    `;

    return card;
}

// Create IPO card element
function createIPOCard(stock) {
    const card = document.createElement('div');
    card.className = 'stock-card';
    card.onclick = () => openStockProfile(stock);

    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';
    const ipoReturn = ((stock.currentPrice - stock.ipoPrice) / stock.ipoPrice * 100).toFixed(2);
    const ipoReturnClass = ipoReturn >= 0 ? 'positive' : 'negative';

    card.innerHTML = `
        <div class="stock-header">
            <div>
                <div class="stock-code">${stock.code}</div>
                <div class="stock-name">${stock.name}</div>
            </div>
            <div class="stock-price">Rp ${formatNumber(stock.currentPrice)}</div>
        </div>
        <div class="stock-info">
            <span>IPO: ${formatDate(stock.ipoDate)}</span>
            <span>IPO Price: Rp ${formatNumber(stock.ipoPrice)}</span>
        </div>
        <div class="stock-change ${ipoReturnClass}">
            IPO Return: ${ipoReturn >= 0 ? '+' : ''}${ipoReturn}%
        </div>
    `;

    return card;
}

// Create potential stock card element
function createPotentialCard(stock) {
    const card = document.createElement('div');
    card.className = 'stock-card';
    card.onclick = () => openStockProfile(stock);

    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';
    const upside = ((stock.targetPrice - stock.price) / stock.price * 100).toFixed(2);

    card.innerHTML = `
        <div class="stock-header">
            <div>
                <div class="stock-code">${stock.code}</div>
                <div class="stock-name">${stock.name}</div>
            </div>
            <div class="stock-price">Rp ${formatNumber(stock.price)}</div>
        </div>
        <div class="stock-info">
            <span>Target: Rp ${formatNumber(stock.targetPrice)}</span>
            <span>Upside: +${upside}%</span>
        </div>
        <div class="stock-change">
            <strong>${stock.recommendation}</strong> - ${stock.analyst}
        </div>
    `;

    return card;
}

// Stock profile modal functionality
function setupStockModal() {
    const modal = document.getElementById('stock-modal');
    const closeBtn = modal.querySelector('.close');

    closeBtn.onclick = function () {
        modal.style.display = 'none';
    };

    window.onclick = function (event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };
}

function openStockProfile(stock) {
    const modal = document.getElementById('stock-modal');
    const profileDiv = document.getElementById('stock-profile');

    profileDiv.innerHTML = createStockProfileHTML(stock);
    modal.style.display = 'block';

    // Initialize chart after modal is shown
    setTimeout(() => {
        initializeStockChart(stock.code);
    }, 100);
}

function createStockProfileHTML(stock) {
    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';

    return `
        <div class="stock-profile-header">
            <h2>${stock.code} - ${stock.name}</h2>
            <div class="stock-profile-price">
                <span class="price">Rp ${formatNumber(stock.price)}</span>
                <span class="change ${changeClass}">
                    ${changeSign}${stock.change} (${changeSign}${stock.changePercent.toFixed(2)}%)
                </span>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-controls">
                <button class="chart-btn active" data-period="daily">1D</button>
                <button class="chart-btn" data-period="weekly">1W</button>
                <button class="chart-btn" data-period="monthly">1M</button>
                <button class="chart-btn" data-period="quarterly">3M</button>
                <button class="chart-btn" data-period="yearly">1Y</button>
            </div>
            <canvas id="stock-chart" width="600" height="300"></canvas>
        </div>
        
        <div class="stock-fundamentals">
            <h3>Data Fundamental</h3>
            <div class="fundamentals-grid">
                <div class="fundamental-item">
                    <span class="label">Market Cap:</span>
                    <span class="value">Rp ${formatNumber(stock.marketCap / 1000000)}M</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">P/E Ratio:</span>
                    <span class="value">${stock.pe}</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">ROE:</span>
                    <span class="value">${stock.roe}%</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">ROA:</span>
                    <span class="value">${stock.roa}%</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">Debt Ratio:</span>
                    <span class="value">${(stock.debtRatio * 100).toFixed(1)}%</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">Volume:</span>
                    <span class="value">${formatVolume(stock.volume)}</span>
                </div>
            </div>
        </div>
    `;
}

// Utility functions
function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

function formatVolume(volume) {
    if (volume >= 1000000000) {
        return (volume / 1000000000).toFixed(1) + 'B';
    } else if (volume >= 1000000) {
        return (volume / 1000000).toFixed(1) + 'M';
    } else if (volume >= 1000) {
        return (volume / 1000).toFixed(1) + 'K';
    }
    return volume.toString();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Loading and notification functions
function showLoadingState() {
    // Add loading overlay
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loading-overlay';
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading real-time data...</p>
        </div>
    `;
    document.body.appendChild(loadingOverlay);
}

function hideLoadingState() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

function showErrorMessage(message) {
    showNotification(message, 'error');
}

function showSuccessMessage(message) {
    showNotification(message, 'success');
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="close-notification">&times;</button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);

    // Close button functionality
    notification.querySelector('.close-notification').onclick = () => {
        notification.remove();
    };
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Enhanced stock profile with real-time data
async function openStockProfile(stock) {
    const modal = document.getElementById('stock-modal');
    const profileDiv = document.getElementById('stock-profile');

    // Show loading in modal
    profileDiv.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading stock data...</p>
        </div>
    `;

    modal.style.display = 'block';

    try {
        // Get real-time stock profile data
        const stockProfile = await dataManager.getStockProfile(stock.code);

        profileDiv.innerHTML = createStockProfileHTML(stockProfile.profile);

        // Initialize chart with real-time data
        setTimeout(() => {
            initializeStockChart(stock.code, stockProfile.chart);
        }, 100);

    } catch (error) {
        console.error('Failed to load stock profile:', error);

        // Fallback to static data
        profileDiv.innerHTML = createStockProfileHTML(stock);

        setTimeout(() => {
            initializeStockChart(stock.code);
        }, 100);
    }
}
