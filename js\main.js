// Main JavaScript for StockScreener Application

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupNavigation();
    setupMobileMenu();
    loadDashboard();
    loadIPOStocks();
    loadPotentialStocks();
    loadTopGainers();
    setupStockModal();
}

// Navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const quickButtons = document.querySelectorAll('[data-section]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
            updateActiveNav(this);
        });
    });
    
    quickButtons.forEach(button => {
        button.addEventListener('click', function() {
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
            updateActiveNav(document.querySelector(`[data-section="${sectionId}"].nav-link`));
        });
    });
}

function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
}

function updateActiveNav(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// Mobile menu functionality
function setupMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navList = document.querySelector('.nav-list');
    
    mobileToggle.addEventListener('click', function() {
        navList.style.display = navList.style.display === 'flex' ? 'none' : 'flex';
    });
}

// Dashboard functionality
function loadDashboard() {
    const featuredGrid = document.getElementById('featured-stocks-grid');
    if (!featuredGrid) return;
    
    featuredGrid.innerHTML = '';
    
    stockData.featured.forEach(stock => {
        const stockCard = createStockCard(stock);
        featuredGrid.appendChild(stockCard);
    });
}

// IPO stocks functionality
function loadIPOStocks() {
    const ipoList = document.getElementById('ipo-list');
    if (!ipoList) return;
    
    ipoList.innerHTML = '';
    
    stockData.ipo.forEach(stock => {
        const ipoCard = createIPOCard(stock);
        ipoList.appendChild(ipoCard);
    });
}

// Potential stocks functionality
function loadPotentialStocks() {
    const potentialList = document.getElementById('potential-stocks');
    if (!potentialList) return;
    
    potentialList.innerHTML = '';
    
    stockData.potential.forEach(stock => {
        const potentialCard = createPotentialCard(stock);
        potentialList.appendChild(potentialCard);
    });
}

// Top gainers functionality
function loadTopGainers() {
    const gainersList = document.getElementById('gainers-list');
    if (!gainersList) return;
    
    gainersList.innerHTML = '';
    
    stockData.gainers.forEach(stock => {
        const gainerCard = createStockCard(stock);
        gainersList.appendChild(gainerCard);
    });
}

// Create stock card element
function createStockCard(stock) {
    const card = document.createElement('div');
    card.className = 'stock-card';
    card.onclick = () => openStockProfile(stock);
    
    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';
    
    card.innerHTML = `
        <div class="stock-header">
            <div>
                <div class="stock-code">${stock.code}</div>
                <div class="stock-name">${stock.name}</div>
            </div>
            <div class="stock-price">Rp ${formatNumber(stock.price)}</div>
        </div>
        <div class="stock-change ${changeClass}">
            ${changeSign}${stock.change} (${changeSign}${stock.changePercent.toFixed(2)}%)
        </div>
        <div class="stock-info">
            <span>Volume: ${formatVolume(stock.volume)}</span>
            <span>P/E: ${stock.pe}</span>
        </div>
    `;
    
    return card;
}

// Create IPO card element
function createIPOCard(stock) {
    const card = document.createElement('div');
    card.className = 'stock-card';
    card.onclick = () => openStockProfile(stock);
    
    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';
    const ipoReturn = ((stock.currentPrice - stock.ipoPrice) / stock.ipoPrice * 100).toFixed(2);
    const ipoReturnClass = ipoReturn >= 0 ? 'positive' : 'negative';
    
    card.innerHTML = `
        <div class="stock-header">
            <div>
                <div class="stock-code">${stock.code}</div>
                <div class="stock-name">${stock.name}</div>
            </div>
            <div class="stock-price">Rp ${formatNumber(stock.currentPrice)}</div>
        </div>
        <div class="stock-info">
            <span>IPO: ${formatDate(stock.ipoDate)}</span>
            <span>IPO Price: Rp ${formatNumber(stock.ipoPrice)}</span>
        </div>
        <div class="stock-change ${ipoReturnClass}">
            IPO Return: ${ipoReturn >= 0 ? '+' : ''}${ipoReturn}%
        </div>
    `;
    
    return card;
}

// Create potential stock card element
function createPotentialCard(stock) {
    const card = document.createElement('div');
    card.className = 'stock-card';
    card.onclick = () => openStockProfile(stock);
    
    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';
    const upside = ((stock.targetPrice - stock.price) / stock.price * 100).toFixed(2);
    
    card.innerHTML = `
        <div class="stock-header">
            <div>
                <div class="stock-code">${stock.code}</div>
                <div class="stock-name">${stock.name}</div>
            </div>
            <div class="stock-price">Rp ${formatNumber(stock.price)}</div>
        </div>
        <div class="stock-info">
            <span>Target: Rp ${formatNumber(stock.targetPrice)}</span>
            <span>Upside: +${upside}%</span>
        </div>
        <div class="stock-change">
            <strong>${stock.recommendation}</strong> - ${stock.analyst}
        </div>
    `;
    
    return card;
}

// Stock profile modal functionality
function setupStockModal() {
    const modal = document.getElementById('stock-modal');
    const closeBtn = modal.querySelector('.close');
    
    closeBtn.onclick = function() {
        modal.style.display = 'none';
    };
    
    window.onclick = function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };
}

function openStockProfile(stock) {
    const modal = document.getElementById('stock-modal');
    const profileDiv = document.getElementById('stock-profile');
    
    profileDiv.innerHTML = createStockProfileHTML(stock);
    modal.style.display = 'block';
    
    // Initialize chart after modal is shown
    setTimeout(() => {
        initializeStockChart(stock.code);
    }, 100);
}

function createStockProfileHTML(stock) {
    const changeClass = stock.change >= 0 ? 'positive' : 'negative';
    const changeSign = stock.change >= 0 ? '+' : '';
    
    return `
        <div class="stock-profile-header">
            <h2>${stock.code} - ${stock.name}</h2>
            <div class="stock-profile-price">
                <span class="price">Rp ${formatNumber(stock.price)}</span>
                <span class="change ${changeClass}">
                    ${changeSign}${stock.change} (${changeSign}${stock.changePercent.toFixed(2)}%)
                </span>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-controls">
                <button class="chart-btn active" data-period="daily">1D</button>
                <button class="chart-btn" data-period="weekly">1W</button>
                <button class="chart-btn" data-period="monthly">1M</button>
                <button class="chart-btn" data-period="quarterly">3M</button>
                <button class="chart-btn" data-period="yearly">1Y</button>
            </div>
            <canvas id="stock-chart" width="600" height="300"></canvas>
        </div>
        
        <div class="stock-fundamentals">
            <h3>Data Fundamental</h3>
            <div class="fundamentals-grid">
                <div class="fundamental-item">
                    <span class="label">Market Cap:</span>
                    <span class="value">Rp ${formatNumber(stock.marketCap / 1000000)}M</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">P/E Ratio:</span>
                    <span class="value">${stock.pe}</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">ROE:</span>
                    <span class="value">${stock.roe}%</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">ROA:</span>
                    <span class="value">${stock.roa}%</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">Debt Ratio:</span>
                    <span class="value">${(stock.debtRatio * 100).toFixed(1)}%</span>
                </div>
                <div class="fundamental-item">
                    <span class="label">Volume:</span>
                    <span class="value">${formatVolume(stock.volume)}</span>
                </div>
            </div>
        </div>
    `;
}

// Utility functions
function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

function formatVolume(volume) {
    if (volume >= 1000000000) {
        return (volume / 1000000000).toFixed(1) + 'B';
    } else if (volume >= 1000000) {
        return (volume / 1000000).toFixed(1) + 'M';
    } else if (volume >= 1000) {
        return (volume / 1000).toFixed(1) + 'K';
    }
    return volume.toString();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}
