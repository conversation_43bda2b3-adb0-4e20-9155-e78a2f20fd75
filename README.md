# StockScreener - Platform Screening Saham Indonesia

Platform web untuk screening dan analisis saham Indonesia dengan fitur lengkap untuk investor dan trader.

## 🚀 Fitur Utama

### 1. Dashboard Utama
- <PERSON><PERSON><PERSON> pasar (IHSG, volume trading)
- <PERSON>ham pilihan dengan informasi real-time
- Aks<PERSON> cepat ke semua fitur

### 2. IPO Saham Terbaru
- Daftar saham yang baru listing
- Perbandingan harga IPO vs harga saat ini
- Return investasi sejak IPO
- Informasi tanggal listing

### 3. <PERSON>ham Berpotensi Naik
- Rekomendasi saham berdasarkan analisis fundamental
- Target price dan upside potential
- Rating dan analisis dari sistem

### 4. Top Gainers Harian
- Saham dengan kenaikan tertinggi hari ini
- Persentase kenaikan dan volume trading
- Informasi sektor dan fundamental

### 5. Profil Saham Detail
- **Chart Harga Interaktif** dengan timeframe:
  - <PERSON><PERSON> (1D)
  - Mingguan (1W) 
  - Bulanan (1M)
  - 3 Bulan (3M)
  - <PERSON><PERSON><PERSON> (1Y)
- **Data Fundamental**:
  - Market Capitalization
  - P/E Ratio
  - ROE (Return on Equity)
  - ROA (Return on Assets)
  - Debt Ratio
  - Volume Trading

### 6. Kalkulator Keuntungan & Kerugian
- Perhitungan P&L otomatis
- Input: harga beli, harga jual, jumlah lot, biaya broker
- Breakdown biaya transaksi lengkap
- Skenario keuntungan/kerugian
- Perhitungan break-even price
- Support untuk berbagai kode saham

### 7. Drawing & Writing Tool
- Canvas untuk menggambar dan menulis manual
- Tools tersedia:
  - Pen (pulpen)
  - Eraser (penghapus)
  - Text (teks)
  - Line (garis)
  - Rectangle (persegi)
  - Circle (lingkaran)
- Pengaturan warna dan ukuran brush
- Simpan gambar sebagai PNG
- Support touch untuk mobile

## 🛠️ Teknologi yang Digunakan

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Chart Library**: Chart.js untuk visualisasi data
- **Icons**: Font Awesome
- **Design**: Responsive design dengan CSS Grid & Flexbox
- **Canvas**: HTML5 Canvas API untuk drawing tool

## 📁 Struktur Proyek

```
coba/
├── index.html              # Halaman utama
├── css/
│   └── style.css          # Stylesheet utama
├── js/
│   ├── main.js            # JavaScript utama & navigasi
│   ├── data.js            # Data dummy saham
│   ├── chart.js           # Fungsi chart dan visualisasi
│   ├── calculator.js      # Kalkulator P&L
│   └── drawing.js         # Drawing tool
└── README.md              # Dokumentasi
```

## 🚀 Cara Menjalankan

1. Clone atau download project
2. Buka `index.html` di browser
3. Tidak memerlukan server khusus (static files)

## 📱 Responsive Design

Aplikasi telah dioptimasi untuk berbagai ukuran layar:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## 🎯 Fitur Data

### Data Saham Tersedia
- **Featured Stocks**: BBCA, BBRI, TLKM, ASII
- **IPO Stocks**: BUKA, GOTO, AMRT
- **Potential Gainers**: UNVR, ICBP, INTP
- **Top Gainers**: MDKA, ANTM, PTBA, INCO

### Data Fundamental
Setiap saham dilengkapi dengan:
- Harga real-time (simulasi)
- Perubahan harian
- Volume trading
- Market cap
- Rasio keuangan (P/E, ROE, ROA, Debt Ratio)

## 🔧 Kustomisasi

### Menambah Data Saham Baru
Edit file `js/data.js` dan tambahkan data saham baru ke array yang sesuai:

```javascript
stockData.featured.push({
    code: "KODE",
    name: "Nama Perusahaan",
    sector: "Sektor",
    price: 1000,
    change: 50,
    changePercent: 5.0,
    // ... data lainnya
});
```

### Mengubah Tema Warna
Edit variabel warna di `css/style.css`:

```css
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}
```

## 📊 Fitur Chart

Chart menggunakan Chart.js dengan fitur:
- Interactive tooltips
- Responsive design
- Multiple timeframes
- Smooth animations
- Real-time data updates (simulasi)

## 🧮 Kalkulator P&L

Kalkulator mencakup:
- Biaya broker (default 0.15%)
- Pajak penjualan (0.1%)
- Perhitungan lot (1 lot = 100 saham)
- Break-even analysis
- Skenario profit/loss

## 🎨 Drawing Tool

Fitur drawing tool:
- Multi-tool support
- Color picker
- Adjustable brush size
- Save to PNG
- Touch support untuk mobile
- Undo/Redo functionality

## 🔮 Pengembangan Selanjutnya

Fitur yang bisa ditambahkan:
- Integrasi API data saham real-time
- Sistem notifikasi price alert
- Portfolio tracking
- Technical analysis indicators
- Export data ke Excel/PDF
- User authentication
- Watchlist personal
- Social trading features

## 📝 Lisensi

Project ini dibuat untuk tujuan edukasi dan demonstrasi.

## 🤝 Kontribusi

Silakan fork project ini dan submit pull request untuk perbaikan atau penambahan fitur.

---

**StockScreener** - Platform screening saham Indonesia yang lengkap dan mudah digunakan! 📈
