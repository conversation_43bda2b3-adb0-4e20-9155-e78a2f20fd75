// Data Manager for Real-time Stock Data

class DataManager {
    constructor() {
        this.subscribers = new Map();
        this.updateIntervals = new Map();
        this.isRealTimeEnabled = FEATURES.realTimeData;
        this.lastUpdateTime = new Map();
    }

    // Subscribe to data updates
    subscribe(dataType, callback, updateInterval = 30000) {
        if (!this.subscribers.has(dataType)) {
            this.subscribers.set(dataType, new Set());
        }
        
        this.subscribers.get(dataType).add(callback);
        
        // Set up auto-update if enabled
        if (this.isRealTimeEnabled && FEATURES.autoRefresh) {
            this.setupAutoUpdate(dataType, updateInterval);
        }
        
        console.log(`Subscribed to ${dataType} updates`);
    }

    // Unsubscribe from data updates
    unsubscribe(dataType, callback) {
        if (this.subscribers.has(dataType)) {
            this.subscribers.get(dataType).delete(callback);
            
            // Clear interval if no more subscribers
            if (this.subscribers.get(dataType).size === 0) {
                this.clearAutoUpdate(dataType);
            }
        }
    }

    // Notify subscribers of data updates
    notifySubscribers(dataType, data) {
        if (this.subscribers.has(dataType)) {
            this.subscribers.get(dataType).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in subscriber callback for ${dataType}:`, error);
                }
            });
        }
    }

    // Setup auto-update intervals
    setupAutoUpdate(dataType, interval) {
        // Clear existing interval
        this.clearAutoUpdate(dataType);
        
        const intervalId = setInterval(async () => {
            try {
                await this.updateData(dataType);
            } catch (error) {
                console.error(`Auto-update failed for ${dataType}:`, error);
            }
        }, interval);
        
        this.updateIntervals.set(dataType, intervalId);
        console.log(`Auto-update enabled for ${dataType} (${interval}ms)`);
    }

    // Clear auto-update interval
    clearAutoUpdate(dataType) {
        if (this.updateIntervals.has(dataType)) {
            clearInterval(this.updateIntervals.get(dataType));
            this.updateIntervals.delete(dataType);
            console.log(`Auto-update disabled for ${dataType}`);
        }
    }

    // Update specific data type
    async updateData(dataType) {
        try {
            let data;
            
            switch (dataType) {
                case 'featured':
                    data = await this.updateFeaturedStocks();
                    break;
                case 'ipo':
                    data = await this.updateIPOStocks();
                    break;
                case 'potential':
                    data = await this.updatePotentialStocks();
                    break;
                case 'gainers':
                    data = await this.updateTopGainers();
                    break;
                case 'market':
                    data = await this.updateMarketData();
                    break;
                default:
                    throw new Error(`Unknown data type: ${dataType}`);
            }
            
            this.lastUpdateTime.set(dataType, Date.now());
            this.notifySubscribers(dataType, data);
            
            return data;
            
        } catch (error) {
            console.error(`Failed to update ${dataType}:`, error);
            
            // Fallback to static data
            const fallbackData = this.getFallbackData(dataType);
            this.notifySubscribers(dataType, fallbackData);
            
            throw error;
        }
    }

    // Update featured stocks with real-time data
    async updateFeaturedStocks() {
        const symbols = stockData.featured.map(stock => stock.code);
        const realTimeData = await apiService.getMultipleStocks(symbols, 'fundamental');
        
        return realTimeData.map((rtData, index) => {
            const staticData = stockData.featured[index];
            return this.mergeStockData(staticData, rtData);
        });
    }

    // Update IPO stocks
    async updateIPOStocks() {
        const symbols = stockData.ipo.map(stock => stock.code);
        const realTimeData = await apiService.getMultipleStocks(symbols, 'fundamental');
        
        return realTimeData.map((rtData, index) => {
            const staticData = stockData.ipo[index];
            const merged = this.mergeStockData(staticData, rtData);
            
            // Calculate IPO performance
            if (staticData.ipoPrice && rtData.price) {
                merged.ipoReturn = ((rtData.price - staticData.ipoPrice) / staticData.ipoPrice) * 100;
                merged.ipoReturnClass = merged.ipoReturn >= 0 ? 'positive' : 'negative';
            }
            
            return merged;
        });
    }

    // Update potential stocks
    async updatePotentialStocks() {
        const symbols = stockData.potential.map(stock => stock.code);
        const realTimeData = await apiService.getMultipleStocks(symbols, 'fundamental');
        
        return realTimeData.map((rtData, index) => {
            const staticData = stockData.potential[index];
            const merged = this.mergeStockData(staticData, rtData);
            
            // Update upside calculation
            if (staticData.targetPrice && rtData.price) {
                merged.upside = ((staticData.targetPrice - rtData.price) / rtData.price) * 100;
                merged.upsideClass = merged.upside >= 0 ? 'positive' : 'negative';
            }
            
            return merged;
        });
    }

    // Update top gainers (this would ideally come from a market screener API)
    async updateTopGainers() {
        // For now, update existing gainers with real-time data
        const symbols = stockData.gainers.map(stock => stock.code);
        const realTimeData = await apiService.getMultipleStocks(symbols, 'fundamental');
        
        const updated = realTimeData.map((rtData, index) => {
            const staticData = stockData.gainers[index];
            return this.mergeStockData(staticData, rtData);
        });
        
        // Sort by change percentage (descending)
        return updated.sort((a, b) => b.changePercent - a.changePercent);
    }

    // Update market overview data
    async updateMarketData() {
        try {
            // Get IHSG data (using ^JKSE symbol for Jakarta Composite Index)
            const ihsgData = await apiService.getStockData('^JKSE', 'quote');
            
            return {
                ihsg: {
                    value: ihsgData.price || marketData.ihsg.value,
                    change: ihsgData.change || marketData.ihsg.change,
                    changePercent: ihsgData.changePercent || marketData.ihsg.changePercent,
                    volume: ihsgData.volume || marketData.ihsg.volume
                },
                sectors: marketData.sectors, // Keep static for now
                lastUpdate: Date.now(),
                isRealTime: !ihsgData.isStatic
            };
        } catch (error) {
            console.error('Failed to update market data:', error);
            return {
                ...marketData,
                lastUpdate: Date.now(),
                isRealTime: false
            };
        }
    }

    // Merge static and real-time data
    mergeStockData(staticData, realTimeData) {
        return {
            ...staticData,
            price: realTimeData.price || staticData.price,
            change: realTimeData.change || staticData.change,
            changePercent: realTimeData.changePercent || staticData.changePercent,
            volume: realTimeData.volume || staticData.volume,
            marketCap: realTimeData.marketCap || staticData.marketCap,
            previousClose: realTimeData.previousClose,
            dayHigh: realTimeData.dayHigh,
            dayLow: realTimeData.dayLow,
            lastUpdate: realTimeData.timestamp || Date.now(),
            isRealTime: !realTimeData.isStatic,
            dataSource: realTimeData.isStatic ? 'static' : 'api'
        };
    }

    // Get fallback data when API fails
    getFallbackData(dataType) {
        console.log(`Using fallback data for ${dataType}`);
        
        switch (dataType) {
            case 'featured':
                return stockData.featured.map(stock => ({
                    ...stock,
                    isRealTime: false,
                    dataSource: 'static',
                    lastUpdate: Date.now()
                }));
            case 'ipo':
                return stockData.ipo.map(stock => ({
                    ...stock,
                    isRealTime: false,
                    dataSource: 'static',
                    lastUpdate: Date.now()
                }));
            case 'potential':
                return stockData.potential.map(stock => ({
                    ...stock,
                    isRealTime: false,
                    dataSource: 'static',
                    lastUpdate: Date.now()
                }));
            case 'gainers':
                return stockData.gainers.map(stock => ({
                    ...stock,
                    isRealTime: false,
                    dataSource: 'static',
                    lastUpdate: Date.now()
                }));
            case 'market':
                return {
                    ...marketData,
                    isRealTime: false,
                    dataSource: 'static',
                    lastUpdate: Date.now()
                };
            default:
                return null;
        }
    }

    // Get single stock data with real-time updates
    async getStockProfile(symbol) {
        try {
            const [quoteData, chartData] = await Promise.all([
                apiService.getStockData(symbol, 'fundamental'),
                apiService.getStockData(symbol, 'chart')
            ]);
            
            // Find static data for additional info
            const allStocks = [
                ...stockData.featured,
                ...stockData.ipo,
                ...stockData.potential,
                ...stockData.gainers
            ];
            
            const staticData = allStocks.find(stock => stock.code === symbol) || {};
            
            return {
                profile: this.mergeStockData(staticData, quoteData),
                chart: chartData
            };
            
        } catch (error) {
            console.error(`Failed to get stock profile for ${symbol}:`, error);
            
            // Return static data as fallback
            const allStocks = [
                ...stockData.featured,
                ...stockData.ipo,
                ...stockData.potential,
                ...stockData.gainers
            ];
            
            const staticData = allStocks.find(stock => stock.code === symbol);
            
            return {
                profile: {
                    ...staticData,
                    isRealTime: false,
                    dataSource: 'static',
                    lastUpdate: Date.now()
                },
                chart: priceHistory[symbol] || null
            };
        }
    }

    // Manual refresh for specific data type
    async refreshData(dataType) {
        console.log(`Manual refresh triggered for ${dataType}`);
        return await this.updateData(dataType);
    }

    // Enable/disable real-time updates
    setRealTimeEnabled(enabled) {
        this.isRealTimeEnabled = enabled;
        
        if (enabled) {
            // Restart auto-updates for all subscribed data types
            this.subscribers.forEach((callbacks, dataType) => {
                if (callbacks.size > 0) {
                    const interval = UPDATE_INTERVALS[dataType] || 30000;
                    this.setupAutoUpdate(dataType, interval);
                }
            });
        } else {
            // Stop all auto-updates
            this.updateIntervals.forEach((intervalId, dataType) => {
                this.clearAutoUpdate(dataType);
            });
        }
        
        console.log(`Real-time updates ${enabled ? 'enabled' : 'disabled'}`);
    }

    // Get last update time for data type
    getLastUpdateTime(dataType) {
        return this.lastUpdateTime.get(dataType) || 0;
    }

    // Check if data is stale
    isDataStale(dataType, maxAge = 60000) { // 1 minute default
        const lastUpdate = this.getLastUpdateTime(dataType);
        return (Date.now() - lastUpdate) > maxAge;
    }

    // Cleanup when page unloads
    cleanup() {
        this.updateIntervals.forEach((intervalId) => {
            clearInterval(intervalId);
        });
        this.updateIntervals.clear();
        this.subscribers.clear();
        console.log('DataManager cleaned up');
    }
}

// Create global instance
const dataManager = new DataManager();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    dataManager.cleanup();
});
