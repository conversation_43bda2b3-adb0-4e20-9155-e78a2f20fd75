// Chart functionality for StockScreener Application

let currentChart = null;

function initializeStockChart(stockCode) {
    const canvas = document.getElementById('stock-chart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart if it exists
    if (currentChart) {
        currentChart.destroy();
    }
    
    // Setup chart controls
    setupChartControls(stockCode);
    
    // Initialize with daily data
    createChart(ctx, stockCode, 'daily');
}

function setupChartControls(stockCode) {
    const chartButtons = document.querySelectorAll('.chart-btn');
    
    chartButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            chartButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get period and update chart
            const period = this.getAttribute('data-period');
            updateChart(stockCode, period);
        });
    });
}

function createChart(ctx, stockCode, period) {
    const data = getChartData(stockCode, period);
    const labels = generateLabels(period, data.length);
    
    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: `${stockCode} Price`,
                data: data,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: Rp ${formatNumber(context.parsed.y)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: getPeriodLabel(period)
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Price (Rp)'
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + formatNumber(value);
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });
}

function updateChart(stockCode, period) {
    if (!currentChart) return;
    
    const data = getChartData(stockCode, period);
    const labels = generateLabels(period, data.length);
    
    currentChart.data.labels = labels;
    currentChart.data.datasets[0].data = data;
    currentChart.options.scales.x.title.text = getPeriodLabel(period);
    
    currentChart.update('active');
}

function getChartData(stockCode, period) {
    // Check if we have historical data for this stock
    if (priceHistory[stockCode] && priceHistory[stockCode][period]) {
        return priceHistory[stockCode][period];
    }
    
    // Generate dummy data if not available
    return generateDummyPriceData(period);
}

function generateDummyPriceData(period) {
    const basePrice = 5000;
    const volatility = 0.02; // 2% volatility
    let dataPoints;
    
    switch (period) {
        case 'daily':
            dataPoints = 5;
            break;
        case 'weekly':
            dataPoints = 7;
            break;
        case 'monthly':
            dataPoints = 30;
            break;
        case 'quarterly':
            dataPoints = 90;
            break;
        case 'yearly':
            dataPoints = 365;
            break;
        default:
            dataPoints = 30;
    }
    
    const data = [];
    let currentPrice = basePrice;
    
    for (let i = 0; i < dataPoints; i++) {
        // Random walk with slight upward trend
        const change = (Math.random() - 0.48) * volatility * currentPrice;
        currentPrice += change;
        data.push(Math.round(currentPrice));
    }
    
    return data;
}

function generateLabels(period, dataLength) {
    const labels = [];
    const now = new Date();
    
    for (let i = dataLength - 1; i >= 0; i--) {
        const date = new Date(now);
        
        switch (period) {
            case 'daily':
                date.setHours(date.getHours() - i);
                labels.push(date.toLocaleTimeString('id-ID', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                }));
                break;
            case 'weekly':
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('id-ID', { 
                    weekday: 'short' 
                }));
                break;
            case 'monthly':
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('id-ID', { 
                    month: 'short', 
                    day: 'numeric' 
                }));
                break;
            case 'quarterly':
                date.setDate(date.getDate() - i * 3);
                labels.push(date.toLocaleDateString('id-ID', { 
                    month: 'short', 
                    day: 'numeric' 
                }));
                break;
            case 'yearly':
                date.setDate(date.getDate() - i * 7);
                labels.push(date.toLocaleDateString('id-ID', { 
                    month: 'short', 
                    day: 'numeric' 
                }));
                break;
            default:
                labels.push(`Point ${dataLength - i}`);
        }
    }
    
    return labels;
}

function getPeriodLabel(period) {
    switch (period) {
        case 'daily':
            return 'Time (Hours)';
        case 'weekly':
            return 'Days';
        case 'monthly':
            return 'Date';
        case 'quarterly':
            return 'Date (3 Months)';
        case 'yearly':
            return 'Date (1 Year)';
        default:
            return 'Time';
    }
}

// Market overview chart for dashboard
function createMarketOverviewChart() {
    const canvas = document.getElementById('market-overview-chart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: marketData.sectors.map(sector => sector.name),
            datasets: [{
                data: marketData.sectors.map(sector => Math.abs(sector.change)),
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6f42c1'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const sector = marketData.sectors[context.dataIndex];
                            return `${sector.name}: ${sector.change > 0 ? '+' : ''}${sector.change}%`;
                        }
                    }
                }
            }
        }
    });
}

// Initialize market overview chart when dashboard loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(createMarketOverviewChart, 500);
});
