// Profit & Loss Calculator for StockScreener Application

document.addEventListener('DOMContentLoaded', function() {
    initializeCalculator();
});

function initializeCalculator() {
    const form = document.getElementById('profit-loss-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateProfitLoss();
    });
    
    // Add real-time calculation on input change
    const inputs = form.querySelectorAll('input[type="number"]');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            if (isFormValid()) {
                calculateProfitLoss();
            }
        });
    });
    
    // Add stock code suggestions
    setupStockCodeSuggestions();
}

function calculateProfitLoss() {
    const stockCode = document.getElementById('stock-code').value.toUpperCase();
    const buyPrice = parseFloat(document.getElementById('buy-price').value) || 0;
    const sellPrice = parseFloat(document.getElementById('sell-price').value) || 0;
    const lots = parseInt(document.getElementById('lots').value) || 0;
    const brokerFee = parseFloat(document.getElementById('broker-fee').value) || 0.15;
    
    if (buyPrice <= 0 || lots <= 0) {
        hideCalculationResult();
        return;
    }
    
    const calculation = performCalculation(buyPrice, sellPrice, lots, brokerFee);
    displayCalculationResult(stockCode, calculation);
}

function performCalculation(buyPrice, sellPrice, lots, brokerFeePercent) {
    const sharesPerLot = 100; // Standard lot size in Indonesia
    const totalShares = lots * sharesPerLot;
    
    // Calculate buy transaction
    const buyValue = buyPrice * totalShares;
    const buyBrokerFee = (buyValue * brokerFeePercent) / 100;
    const buyTax = 0; // No tax on buying
    const totalBuyCost = buyValue + buyBrokerFee + buyTax;
    
    // Calculate sell transaction (if sell price is provided)
    let sellValue = 0;
    let sellBrokerFee = 0;
    let sellTax = 0;
    let totalSellProceeds = 0;
    let netProfitLoss = 0;
    let profitLossPercent = 0;
    
    if (sellPrice > 0) {
        sellValue = sellPrice * totalShares;
        sellBrokerFee = (sellValue * brokerFeePercent) / 100;
        sellTax = sellValue * 0.001; // 0.1% tax on selling
        totalSellProceeds = sellValue - sellBrokerFee - sellTax;
        
        netProfitLoss = totalSellProceeds - totalBuyCost;
        profitLossPercent = (netProfitLoss / totalBuyCost) * 100;
    }
    
    return {
        totalShares,
        buyValue,
        buyBrokerFee,
        buyTax,
        totalBuyCost,
        sellValue,
        sellBrokerFee,
        sellTax,
        totalSellProceeds,
        netProfitLoss,
        profitLossPercent,
        averageBuyPrice: buyPrice,
        averageSellPrice: sellPrice
    };
}

function displayCalculationResult(stockCode, calc) {
    const resultDiv = document.getElementById('calculation-result');
    if (!resultDiv) return;
    
    const isProfit = calc.netProfitLoss >= 0;
    const profitLossClass = isProfit ? 'positive' : 'negative';
    const profitLossSign = isProfit ? '+' : '';
    
    let resultHTML = `
        <h3>Hasil Perhitungan ${stockCode || 'Saham'}</h3>
        
        <div class="calculation-section">
            <h4>Detail Pembelian</h4>
            <div class="result-item">
                <span>Jumlah Saham:</span>
                <span>${formatNumber(calc.totalShares)} lembar</span>
            </div>
            <div class="result-item">
                <span>Nilai Pembelian:</span>
                <span>Rp ${formatNumber(calc.buyValue)}</span>
            </div>
            <div class="result-item">
                <span>Biaya Broker:</span>
                <span>Rp ${formatNumber(calc.buyBrokerFee)}</span>
            </div>
            <div class="result-item">
                <span><strong>Total Biaya Beli:</strong></span>
                <span><strong>Rp ${formatNumber(calc.totalBuyCost)}</strong></span>
            </div>
        </div>
    `;
    
    if (calc.sellValue > 0) {
        resultHTML += `
            <div class="calculation-section">
                <h4>Detail Penjualan</h4>
                <div class="result-item">
                    <span>Nilai Penjualan:</span>
                    <span>Rp ${formatNumber(calc.sellValue)}</span>
                </div>
                <div class="result-item">
                    <span>Biaya Broker:</span>
                    <span>Rp ${formatNumber(calc.sellBrokerFee)}</span>
                </div>
                <div class="result-item">
                    <span>Pajak (0.1%):</span>
                    <span>Rp ${formatNumber(calc.sellTax)}</span>
                </div>
                <div class="result-item">
                    <span><strong>Total Hasil Jual:</strong></span>
                    <span><strong>Rp ${formatNumber(calc.totalSellProceeds)}</strong></span>
                </div>
            </div>
            
            <div class="calculation-section">
                <h4>Keuntungan/Kerugian</h4>
                <div class="result-item ${profitLossClass}">
                    <span><strong>Net P&L:</strong></span>
                    <span><strong>${profitLossSign}Rp ${formatNumber(Math.abs(calc.netProfitLoss))}</strong></span>
                </div>
                <div class="result-item ${profitLossClass}">
                    <span><strong>Persentase P&L:</strong></span>
                    <span><strong>${profitLossSign}${calc.profitLossPercent.toFixed(2)}%</strong></span>
                </div>
            </div>
        `;
    } else {
        resultHTML += `
            <div class="calculation-section">
                <h4>Informasi Tambahan</h4>
                <div class="result-item">
                    <span>Harga Rata-rata:</span>
                    <span>Rp ${formatNumber(calc.averageBuyPrice)}</span>
                </div>
                <div class="result-item">
                    <span>Break Even Price:</span>
                    <span>Rp ${formatNumber(calculateBreakEvenPrice(calc))}</span>
                </div>
            </div>
        `;
    }
    
    // Add profit/loss scenarios
    if (calc.sellValue === 0) {
        resultHTML += generateProfitLossScenarios(calc);
    }
    
    resultDiv.innerHTML = resultHTML;
    resultDiv.style.display = 'block';
}

function calculateBreakEvenPrice(calc) {
    // Break even price considering all fees and taxes
    const totalFees = calc.buyBrokerFee + (calc.totalShares * 0.001 * calc.averageBuyPrice); // Estimated sell tax
    const brokerFeeOnSell = 0.0015; // 0.15% broker fee on sell
    
    // Break even = (Total Buy Cost + Sell Fees) / (Total Shares * (1 - Broker Fee on Sell))
    const breakEven = (calc.totalBuyCost) / (calc.totalShares * (1 - brokerFeeOnSell - 0.001));
    
    return Math.ceil(breakEven);
}

function generateProfitLossScenarios(calc) {
    const scenarios = [
        { label: '5% Profit', multiplier: 1.05 },
        { label: '10% Profit', multiplier: 1.10 },
        { label: '20% Profit', multiplier: 1.20 },
        { label: '5% Loss', multiplier: 0.95 },
        { label: '10% Loss', multiplier: 0.90 }
    ];
    
    let scenarioHTML = `
        <div class="calculation-section">
            <h4>Skenario Keuntungan/Kerugian</h4>
            <div class="scenarios-grid">
    `;
    
    scenarios.forEach(scenario => {
        const targetPrice = Math.round(calc.averageBuyPrice * scenario.multiplier);
        const scenarioCalc = performCalculation(
            calc.averageBuyPrice, 
            targetPrice, 
            calc.totalShares / 100, 
            0.15
        );
        
        const isProfit = scenarioCalc.netProfitLoss >= 0;
        const profitLossClass = isProfit ? 'positive' : 'negative';
        const profitLossSign = isProfit ? '+' : '';
        
        scenarioHTML += `
            <div class="scenario-item">
                <div class="scenario-label">${scenario.label}</div>
                <div class="scenario-price">Rp ${formatNumber(targetPrice)}</div>
                <div class="scenario-result ${profitLossClass}">
                    ${profitLossSign}Rp ${formatNumber(Math.abs(scenarioCalc.netProfitLoss))}
                </div>
            </div>
        `;
    });
    
    scenarioHTML += `
            </div>
        </div>
    `;
    
    return scenarioHTML;
}

function hideCalculationResult() {
    const resultDiv = document.getElementById('calculation-result');
    if (resultDiv) {
        resultDiv.style.display = 'none';
    }
}

function isFormValid() {
    const buyPrice = parseFloat(document.getElementById('buy-price').value) || 0;
    const lots = parseInt(document.getElementById('lots').value) || 0;
    
    return buyPrice > 0 && lots > 0;
}

function setupStockCodeSuggestions() {
    const stockCodeInput = document.getElementById('stock-code');
    if (!stockCodeInput) return;
    
    // Create datalist for stock code suggestions
    const datalist = document.createElement('datalist');
    datalist.id = 'stock-codes';
    
    // Add all stock codes from our data
    const allStocks = [
        ...stockData.featured,
        ...stockData.ipo,
        ...stockData.potential,
        ...stockData.gainers
    ];
    
    const uniqueCodes = [...new Set(allStocks.map(stock => stock.code))];
    
    uniqueCodes.forEach(code => {
        const option = document.createElement('option');
        option.value = code;
        datalist.appendChild(option);
    });
    
    stockCodeInput.setAttribute('list', 'stock-codes');
    stockCodeInput.parentNode.appendChild(datalist);
    
    // Auto-fill current price when stock code is selected
    stockCodeInput.addEventListener('change', function() {
        const selectedCode = this.value.toUpperCase();
        const stock = allStocks.find(s => s.code === selectedCode);
        
        if (stock) {
            document.getElementById('sell-price').placeholder = `Current: ${stock.price}`;
        }
    });
}

// Utility function for number formatting (reuse from main.js)
function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}
