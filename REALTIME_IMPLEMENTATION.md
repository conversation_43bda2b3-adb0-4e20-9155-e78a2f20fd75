# 📊 Real-time Data Implementation Guide

## 🎯 Overview

Panduan lengkap untuk mengimplementasikan data real-time pada StockScreener menggunakan API publik.

## 🚀 Quick Start

### 1. Setup CORS Proxy (Development)

Karena browser memblokir CORS, gunakan salah satu solusi berikut:

**Option A: Public CORS Proxy (Easiest)**
```javascript
// Di js/config.js, ubah:
CORS_SOLUTIONS.active = 'corsAnywhere'; // Default
```

**Option B: Local Development Server**
```bash
# Install cors-anywhere locally
npm install -g cors-anywhere
cors-anywhere
```

**Option C: Disable CORS (Chrome)**
```bash
# Windows
chrome.exe --user-data-dir="C:/Chrome dev session" --disable-web-security

# Mac
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security
```

### 2. Get API Keys (Optional)

**Alpha Vantage (Free)**
1. Daftar di: https://www.alphavantage.co/support/#api-key
2. Update `js/config.js`:
```javascript
API_CONFIG.alphaVantage.apiKey = 'YOUR_API_KEY_HERE';
```

**Finnhub (Free)**
1. Daftar di: https://finnhub.io/register
2. Update `js/config.js`:
```javascript
API_CONFIG.finnhub.apiKey = 'YOUR_API_KEY_HERE';
```

### 3. Enable Real-time Features

```javascript
// Di js/config.js
FEATURES.realTimeData = true;
FEATURES.autoRefresh = true;
```

## 📁 File Structure

```
js/
├── config.js          # API configuration & settings
├── api-service.js     # API service layer
├── data-manager.js    # Data management & caching
├── main.js           # Updated with real-time features
├── data.js           # Static data (fallback)
├── chart.js          # Chart functionality
├── calculator.js     # P&L calculator
└── drawing.js        # Drawing tool
```

## 🔧 Configuration

### API Settings

```javascript
// js/config.js
const API_CONFIG = {
    yahoo: {
        baseUrl: 'https://query1.finance.yahoo.com/v8/finance/chart/',
        rateLimit: 2000,    // 2 seconds between calls
        timeout: 10000,     // 10 seconds timeout
        retries: 3
    }
};
```

### Update Intervals

```javascript
const UPDATE_INTERVALS = {
    dashboard: 30000,      // 30 seconds
    stockProfile: 15000,   // 15 seconds
    topGainers: 60000,     // 1 minute
    marketOverview: 30000, // 30 seconds
    charts: 60000          // 1 minute
};
```

### Cache Configuration

```javascript
const CACHE_CONFIG = {
    priceData: 30000,      // 30 seconds
    fundamentalData: 300000, // 5 minutes
    historicalData: 900000,  // 15 minutes
    maxSize: 100,
    storage: 'localStorage'
};
```

## 🔌 API Integration

### Supported APIs

1. **Yahoo Finance (Primary)**
   - ✅ Free, no API key required
   - ✅ Indonesian stocks (suffix .JK)
   - ✅ Real-time quotes & historical data
   - ❌ Limited fundamental data

2. **Alpha Vantage (Secondary)**
   - ✅ Comprehensive fundamental data
   - ✅ Free tier: 5 calls/minute, 500/day
   - ❌ Requires API key
   - ❌ Limited Indonesian stock coverage

3. **Finnhub (Backup)**
   - ✅ Good API design
   - ✅ Free tier: 60 calls/minute
   - ❌ Requires API key
   - ❌ Limited Indonesian stock coverage

### Stock Symbol Mapping

```javascript
// Indonesian stocks use .JK suffix for Yahoo Finance
const STOCK_SYMBOLS = {
    'BBCA': 'BBCA.JK',
    'BBRI': 'BBRI.JK',
    'TLKM': 'TLKM.JK',
    // ... more symbols
};
```

## 📊 Data Flow

```
User Interface
      ↓
Data Manager (Subscribe/Update)
      ↓
API Service (Rate Limiting/Caching)
      ↓
External APIs (Yahoo Finance, etc.)
      ↓
Cache Layer (localStorage/memory)
      ↓
UI Updates (Real-time indicators)
```

## 🎛️ Usage Examples

### Subscribe to Real-time Updates

```javascript
// Subscribe to featured stocks updates
dataManager.subscribe('featured', (stocks) => {
    console.log('Featured stocks updated:', stocks);
    updateFeaturedStocks(stocks);
}, 30000); // Update every 30 seconds
```

### Manual Data Refresh

```javascript
// Refresh specific data type
await dataManager.refreshData('gainers');

// Get single stock profile
const profile = await dataManager.getStockProfile('BBCA');
```

### Check Market Status

```javascript
const isOpen = apiService.isMarketOpen();
console.log('Market is', isOpen ? 'open' : 'closed');
```

## 🔧 Troubleshooting

### Common Issues

**1. CORS Errors**
```
Access to fetch at 'https://query1.finance.yahoo.com/...' from origin 'file://' has been blocked by CORS policy
```
**Solution**: Use CORS proxy atau setup local server

**2. Rate Limiting**
```
Too Many Requests (429)
```
**Solution**: Increase `rateLimit` di config atau gunakan caching

**3. API Key Issues**
```
Invalid API key
```
**Solution**: Periksa API key di `js/config.js`

### Debug Mode

```javascript
// Enable debug logging
localStorage.setItem('debug', 'true');

// Check cache status
console.log(apiService.cache);

// Check last update times
console.log(dataManager.getLastUpdateTime('featured'));
```

## 🚀 Production Deployment

### 1. Setup Backend Proxy

**Node.js Express Example:**
```javascript
const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
app.use(cors());

app.get('/api/yahoo/:symbol', async (req, res) => {
    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${req.params.symbol}`;
    const response = await fetch(url);
    const data = await response.json();
    res.json(data);
});

app.listen(3001);
```

**Update Config:**
```javascript
CORS_SOLUTIONS.active = 'localProxy';
CORS_SOLUTIONS.localProxy = 'https://your-api-server.com/api/';
```

### 2. Environment Variables

```javascript
// Production config
const API_CONFIG = {
    yahoo: {
        baseUrl: process.env.YAHOO_API_URL || 'https://your-proxy.com/yahoo/',
        rateLimit: process.env.RATE_LIMIT || 1000
    }
};
```

### 3. Error Monitoring

```javascript
// Add error tracking
window.addEventListener('error', (error) => {
    // Send to monitoring service
    console.error('Global error:', error);
});
```

## 📈 Performance Tips

1. **Optimize Update Intervals**
   - Dashboard: 30s
   - Stock Profile: 15s
   - Market Data: 30s

2. **Use Smart Caching**
   - Price data: 30s cache
   - Fundamental: 5min cache
   - Historical: 15min cache

3. **Batch API Calls**
   ```javascript
   // Get multiple stocks in one call
   const stocks = await apiService.getMultipleStocks(['BBCA', 'BBRI', 'TLKM']);
   ```

4. **Lazy Loading**
   - Load data only when section is visible
   - Use intersection observer

## 🔮 Future Enhancements

1. **WebSocket Integration**
   - Real-time streaming data
   - Lower latency updates

2. **Advanced Caching**
   - IndexedDB for large datasets
   - Service Worker for offline support

3. **Data Compression**
   - Gzip API responses
   - Delta updates only

4. **Multiple Data Sources**
   - Aggregate from multiple APIs
   - Fallback chain

## 📝 Testing

### Manual Testing

1. Open browser console
2. Check for API calls: `Network` tab
3. Verify cache: `localStorage` in `Application` tab
4. Test error scenarios: Disable network

### Automated Testing

```javascript
// Test API service
async function testAPIService() {
    try {
        const data = await apiService.getStockData('BBCA', 'quote');
        console.log('✅ API test passed:', data);
    } catch (error) {
        console.error('❌ API test failed:', error);
    }
}
```

## 📞 Support

Jika mengalami masalah:

1. Periksa browser console untuk error
2. Pastikan CORS proxy berfungsi
3. Verifikasi API keys (jika digunakan)
4. Test dengan data static terlebih dahulu

---

**Happy Trading! 📈**
