// API Configuration for StockScreener

const API_CONFIG = {
    // Primary API - Yahoo Finance (No API key required)
    yahoo: {
        baseUrl: 'https://query1.finance.yahoo.com/v8/finance/chart/',
        quotesUrl: 'https://query1.finance.yahoo.com/v7/finance/quote',
        corsProxy: 'https://cors-anywhere.herokuapp.com/', // For development
        rateLimit: 2000, // 2 seconds between calls
        timeout: 10000, // 10 seconds timeout
        retries: 3
    },
    
    // Secondary API - Alpha Vantage (Requires API key)
    alphaVantage: {
        baseUrl: 'https://www.alphavantage.co/query',
        apiKey: 'YOUR_API_KEY_HERE', // Get free key from alphavantage.co
        rateLimit: 12000, // 12 seconds between calls (5 per minute)
        timeout: 15000,
        retries: 2
    },
    
    // Backup API - Finnhub (Requires API key)
    finnhub: {
        baseUrl: 'https://finnhub.io/api/v1/',
        apiKey: 'YOUR_API_KEY_HERE', // Get free key from finnhub.io
        rateLimit: 1000, // 1 second between calls (60 per minute)
        timeout: 10000,
        retries: 2
    }
};

// Indonesian stock symbols mapping
const STOCK_SYMBOLS = {
    // Banking
    'BBCA': 'BBCA.JK',
    'BBRI': 'BBRI.JK',
    'BMRI': 'BMRI.JK',
    'BBNI': 'BBNI.JK',
    
    // Telecommunications
    'TLKM': 'TLKM.JK',
    'ISAT': 'ISAT.JK',
    'EXCL': 'EXCL.JK',
    
    // Consumer Goods
    'UNVR': 'UNVR.JK',
    'INDF': 'INDF.JK',
    'ICBP': 'ICBP.JK',
    
    // Mining
    'ANTM': 'ANTM.JK',
    'PTBA': 'PTBA.JK',
    'INCO': 'INCO.JK',
    'MDKA': 'MDKA.JK',
    
    // Automotive
    'ASII': 'ASII.JK',
    'AUTO': 'AUTO.JK',
    
    // Technology/E-commerce
    'GOTO': 'GOTO.JK',
    'BUKA': 'BUKA.JK',
    
    // Retail
    'AMRT': 'AMRT.JK',
    'MAPI': 'MAPI.JK',
    
    // Cement
    'INTP': 'INTP.JK',
    'SMGR': 'SMGR.JK'
};

// Cache configuration
const CACHE_CONFIG = {
    // Cache duration in milliseconds
    priceData: 30000,      // 30 seconds for price data
    fundamentalData: 300000, // 5 minutes for fundamental data
    historicalData: 900000,  // 15 minutes for historical data
    marketData: 60000,       // 1 minute for market overview
    
    // Maximum cache size (number of items)
    maxSize: 100,
    
    // Storage type
    storage: 'localStorage' // or 'sessionStorage' or 'memory'
};

// Update intervals
const UPDATE_INTERVALS = {
    dashboard: 30000,      // 30 seconds
    stockProfile: 15000,   // 15 seconds
    topGainers: 60000,     // 1 minute
    marketOverview: 30000, // 30 seconds
    charts: 60000          // 1 minute
};

// Error handling configuration
const ERROR_CONFIG = {
    maxRetries: 3,
    retryDelay: 2000,      // 2 seconds
    fallbackToStatic: true, // Use static data if all APIs fail
    showErrorMessages: true,
    logErrors: true
};

// CORS solutions for development
const CORS_SOLUTIONS = {
    // Option 1: Public CORS proxy (for development only)
    corsAnywhere: 'https://cors-anywhere.herokuapp.com/',
    
    // Option 2: Alternative CORS proxies
    allOrigins: 'https://api.allorigins.win/get?url=',
    corsproxy: 'https://corsproxy.io/?',
    
    // Option 3: Local development server
    localProxy: 'http://localhost:3001/api/',
    
    // Current active solution
    active: 'corsAnywhere' // Change this based on what works
};

// Market hours (Indonesian time - WIB)
const MARKET_HOURS = {
    open: { hour: 9, minute: 0 },   // 09:00 WIB
    close: { hour: 16, minute: 0 }, // 16:00 WIB
    timezone: 'Asia/Jakarta',
    
    // Trading days (0 = Sunday, 1 = Monday, etc.)
    tradingDays: [1, 2, 3, 4, 5], // Monday to Friday
    
    // Holidays (add Indonesian market holidays)
    holidays: [
        '2024-01-01', // New Year
        '2024-02-10', // Chinese New Year
        '2024-03-11', // Nyepi
        '2024-03-29', // Good Friday
        '2024-04-10', // Eid al-Fitr
        '2024-04-11', // Eid al-Fitr
        '2024-05-01', // Labor Day
        '2024-05-09', // Ascension Day
        '2024-06-01', // Pancasila Day
        '2024-06-17', // Eid al-Adha
        '2024-08-17', // Independence Day
        '2024-12-25'  // Christmas
    ]
};

// Feature flags
const FEATURES = {
    realTimeData: true,
    autoRefresh: true,
    notifications: false,
    advancedCharts: true,
    fundamentalData: true,
    newsIntegration: false,
    portfolioTracking: false
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        API_CONFIG,
        STOCK_SYMBOLS,
        CACHE_CONFIG,
        UPDATE_INTERVALS,
        ERROR_CONFIG,
        CORS_SOLUTIONS,
        MARKET_HOURS,
        FEATURES
    };
}
