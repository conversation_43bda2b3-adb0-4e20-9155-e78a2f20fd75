/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.logo i {
  margin-right: 0.5rem;
  font-size: 1.8rem;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.mobile-menu-toggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Main Content */
.main {
  margin-top: 80px;
  min-height: calc(100vh - 80px);
}

.section {
  display: none;
  padding: 2rem 0;
}

.section.active {
  display: block;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  color: #333;
}

/* Card Styles */
.card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

/* Dashboard Styles */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.market-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.stat-change {
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
}

.stat-change.positive {
  background-color: #d4edda;
  color: #155724;
}

.stat-change.negative {
  background-color: #f8d7da;
  color: #721c24;
}

.quick-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #1e7e34;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

/* Stock Grid */
.stock-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.stock-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stock-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stock-code {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.stock-price {
  font-size: 1.1rem;
  font-weight: bold;
}

.stock-name {
  color: #666;
  margin-bottom: 0.5rem;
}

.stock-change {
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.9rem;
}

.stock-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

input[type="text"],
input[type="number"] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
}

input[type="text"]:focus,
input[type="number"]:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Calculator Styles */
.calculator-container {
  max-width: 600px;
  margin: 0 auto;
}

.calculation-result {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 5px;
  display: none;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dee2e6;
}

.result-item:last-child {
  border-bottom: none;
  font-weight: bold;
  font-size: 1.1rem;
}

/* Drawing Tool Styles */
.drawing-container {
  text-align: center;
}

.drawing-toolbar {
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

.tool-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-btn.active,
.tool-btn:hover {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

#drawing-canvas {
  border: 2px solid #ddd;
  border-radius: 10px;
  background: white;
  cursor: crosshair;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 2rem;
  border-radius: 10px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  font-size: 2rem;
  cursor: pointer;
  color: #666;
}

.close:hover {
  color: #333;
}

/* Stock Profile Styles */
.stock-profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.stock-profile-price {
  text-align: right;
}

.stock-profile-price .price {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #333;
}

.stock-profile-price .change {
  font-size: 1.1rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  margin-top: 0.5rem;
  display: inline-block;
}

.chart-container {
  margin-bottom: 2rem;
}

.chart-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.chart-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.fundamentals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.fundamental-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.fundamental-item .label {
  font-weight: 500;
  color: #666;
}

.fundamental-item .value {
  font-weight: bold;
  color: #333;
}

/* Calculator Additional Styles */
.calculation-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-left: 4px solid #007bff;
  background-color: #f8f9fa;
}

.calculation-section h4 {
  margin-bottom: 1rem;
  color: #333;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.scenario-item {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scenario-label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #666;
}

.scenario-price {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.scenario-result {
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
}

/* List Styles */
.ipo-list,
.stock-list,
.gainers-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-list {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .market-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .drawing-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }

  #drawing-canvas {
    width: 100%;
    height: 400px;
  }

  .stock-profile-header {
    flex-direction: column;
    text-align: center;
  }

  .stock-profile-price {
    text-align: center;
    margin-top: 1rem;
  }

  .chart-controls {
    flex-wrap: wrap;
  }

  .fundamentals-grid {
    grid-template-columns: 1fr;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .ipo-list,
  .stock-list,
  .gainers-list {
    grid-template-columns: 1fr;
  }
}
